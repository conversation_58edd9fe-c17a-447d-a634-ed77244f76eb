declare namespace Cypress {
  interface Chainable {
    Graphql(
      query: string,
      variables?: unknown
    ): Chainable<Cypress.Response<unknown>>;
    getDataIdCy({
      idAlias,
      options,
    }: {
      idAlias: string;
      options?: Partial<
        Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow
      >;
    }): Cypress.Chainable<JQuery<HTMLElement>>;
  }
  interface RequestOptions extends Loggable, Timeoutable, Failable {
    auth: object;
    body: RequestBody;
    encoding: Encodings;
    followRedirect: boolean;
    form: boolean;
    gzip: boolean;
    headers: object;
    method: HttpMethod;
    qs: object;
    url: string;
  }
}
