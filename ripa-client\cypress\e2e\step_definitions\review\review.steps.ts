import { Before, When } from '@badeball/cypress-cucumber-preprocessor';
import { getByTestId, type as typeIntoTestId } from '../../../support/utility';
import { logInAsReviewer, logInAsOfficer, createAndSubmitForm } from '../../../support/actions';

const expandFirstRow = () => {
  getByTestId('review-row-cell-select-expand').first().click();

  cy.get('.expanded').first().should('be.visible');
  // cy.wait(1000);
};

// const testReviewerId = (reportIndex) => (
//   cy.window().its('store').invoke('getState')
//     .its('Review')
//     .its('userForms')
//     .its(reportIndex)
//     .its('contents')
//     .its('reviewerId').should('eq', 6)
// )

const openReviewDialog = () => {
  getByTestId('review-row-table-cell-actions-review').first().click();

  getByTestId('review-dialog').should('exist');
};

const approveReport = () => {
  getByTestId('review-dialog-approve-top').first().click();

  getByTestId('review-dialog').should('not.exist');
};

const denyReport = () => {
  getByTestId('review-dialog-deny-top').first().click();

  typeIntoTestId('review-feedback-dialog-input', 'Error 101');

  getByTestId('confirm-dialog-yes-button').click();

  getByTestId('review-dialog').should('not.exist');
};

const editReportLocation = () => {
  getByTestId('review-row-cell-select-expand').not('.disabled').first().click();

  getByTestId('review-row-cell-location-field-input')
    .not('.disabled')
    .first()
    .clear()
    .type('2021 Webster Street, Oakland, CA 9999-3333')
    .should('have.value', '2021 Webster Street, Oakland, CA 9999-3333');
};

const editSearchDescription = () => {
  expandFirstRow();

  cy.get('.expanded').first().should('be.visible');

  getByTestId('review-row-row-details-search-field-input').first().click().clear()
  .type('Search description automation test')
  .should('have.value', 'Search description automation test');

  cy.wait(1000); // Some time for data to save
};

const editStopDescriptions = () => {
  expandFirstRow();

  cy.get('.expanded').first().should('be.visible');

  getByTestId('review-row-row-details-stop-field-input').first().clear()
  .type('Search description automation test 1')
  .should('have.value', 'Search description automation test 1');

  getByTestId('review-row-row-details-stop-field-input').eq(1).clear()
  .type('Search description automation test 2')
  .should('have.value', 'Search description automation test 2');

  // cy.wait(1000);
};

Before(() => {
  logInAsOfficer();

  createAndSubmitForm(false);

  logInAsReviewer();
});

When('The user opens the review dialog', () => {
  openReviewDialog();
});

When('The user approves a report', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  openReviewDialog();

  approveReport();
});

When('The user approves a report by clicking the approve icon', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  getByTestId('review-row-table-cell-approve').not('.disabled').first().click();

  getByTestId('review-dialog').should('not.exist');

  // testReviewerId(2);
});

When('The user denies a report', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  openReviewDialog();

  denyReport();
});

When('The user edits a report location', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editReportLocation();
});

When('The user edits a search description', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editSearchDescription();

  // What's the point in logging out here?
  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-signout').click();

  cy.url().should('include', '/log_in');

  logInAsReviewer();

  expandFirstRow();

  getByTestId('review-row-row-details-search-field-input').first().should('have.value', 'Search description automation test');
});

When('The user edits stop descriptions', () => {
  getByTestId('review-filters-under-review').click();
  getByTestId('loading').should('not.exist');

  editStopDescriptions();

  getByTestId('header-navigation').click();

  getByTestId('header-veritone-nav-signout').click();

  cy.url().should('include', 'log_in');

  logInAsReviewer();

  expandFirstRow();

  getByTestId('review-row-row-details-stop-field-input').first().should('have.value', 'Search description automation test 1');

  getByTestId('review-row-row-details-stop-field-input').eq(1).should('have.value', 'Search description automation test 2');
});
