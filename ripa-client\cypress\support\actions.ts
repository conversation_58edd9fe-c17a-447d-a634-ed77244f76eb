import dayjs from 'dayjs';
import { clearSnackBars, clickTestIds, getByTestId, type, typeLogin } from './utility';

export const logInAs = (username: string, password: string, landingPage: string | null = null, subdomain = 'demo'): void => {
  cy.session(
    [username, password, landingPage, subdomain],
    () => {
      cy.visit(`https://${subdomain || 'demo'}.contact-dev.com:2222/log_in`);
      typeLogin('login-username-field', username);
      typeLogin('login-pw-field', password);

      getByTestId('login-submit').click();
      cy.url().should('not.include', '/log_in');
      if (landingPage) {
        cy.url().should('include', landingPage);
      }
    },
    {
      validate() {
        cy.request('/api/v1/users/session').its('status').should('eq', 200);
      },
    }
  );
  cy.visit(`https://${subdomain}.contact-dev.com:2222${landingPage || '/'}`);
  getByTestId('header-navigation').should('exist');
  cy.get('div.auth-provider__loading').should('not.exist');
  getByTestId('loading').should('not.exist');
};

export const logInAsReviewer = (): void => {
  logInAs('demo_reviewer_1', 'ripa_demo', '/review');
};

export const logInAsOfficer = (): void => {
  logInAs('demo_officer_1', 'ripa_demo', '/dashboard');
};

export const logInAsAdmin = (): void => {
  logInAs('demo_admin_1', 'ripa_demo', '/admin/users');
};

export const logInAsSuperAdmin = (): void => {
  logInAs('veritone_cs', 'ripa_demo', '/admin/users', 'veri-admin');
};

export const updateUserSettings = (): void => {
  cy.get('body').then((bodyElement) => {
    if (bodyElement.find('[data-testid="confirm-dialog"]').length > 0) {
      getByTestId('race-of-officer-select').click();
      getByTestId('race-of-officer-select-checkbox-1').click();
      getByTestId('confirm-dialog-yes-button').click({ force: true });
    }
  });
};

export const createAndSubmitForm = (keepOffline: boolean): void => {
  updateUserSettings();
  getByTestId('dashboard-filters-new-report').not('.disabled').click();

  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
  }

  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));

  getByTestId('ripa-form-container-continue').click();

  type('ripa-time-form-input', '00:00');

  getByTestId('ripa-form-container-continue').click();

  // Wait for the response to call form to be visible
  getByTestId('ripa-response-to-call-form').should('be.visible');
  getByTestId('ripa-response-to-call-yes').should('be.visible').click();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-duration-form-20').should('be.visible').click();
  clearSnackBars();
  clickTestIds(['ripa-form-container-continue', 'ripa-location-form-school-switch', 'ripa-location-form-school-autocomplete']);

  cy.get('*[data-option-index=2]').click();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-people-form-people-counter-plus').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-label-form-label-input-0', 'label 0');

  type('ripa-label-form-label-input-1', 'label 1');

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-gender-form-box-0-0').click();

  getByTestId('ripa-gender-form-box-1-3').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-age-form-input-0', '12');

  getByTestId('ripa-age-form-student-switch-0').click();

  type('ripa-age-form-input-1', '22');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-race-form-0-0',
    'ripa-race-form-1-1',
    'ripa-form-container-continue',
    'ripa-disability-form-english-switch-1',
    'ripa-disability-form-disability-switch-person-1',
    'ripa-form-container-continue',
    'ripa-disability-details-form-box-1-4',
    'ripa-form-container-continue',
    'ripa-primary-reason-form-box-k12-0',
    'ripa-form-container-continue',
    'ripa-code-section-form-box-0',
    'ripa-code-section-form-autocomplete-input',
  ]);

  cy.get('*[data-option-index=2]').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-description-form-input', '123456');

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-primary-reason-form-box-3').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-description-form-input', '4563456');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-action-tab-4',
    'ripa-action-taken-form-box-4-0',
    'ripa-action-taken-form-consent-switch-4-0',
    'ripa-action-taken-form-box-4-1',
    'ripa-action-taken-form-box-4-4',
    'ripa-form-container-continue',
    'ripa-search-basis-form-box-5',
    'ripa-form-container-continue',
  ]);

  type('ripa-search-description-form-input', '546456');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-seizure-basis-form-box-1',
    'ripa-form-container-continue',
    'ripa-seizure-type-form-box-1',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-1',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-0',
    'ripa-result-of-stop-form-autocomplete-input-0-0',
  ]);

  cy.get('*[data-option-index=10]').click();

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-box-0-3',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-5',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-1',
    'ripa-result-of-stop-form-autocomplete-input-0-1',
  ]);

  cy.get('*[data-option-index=10]').click();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-form-container-continue').click();

  cy.url().should('include', '/dashboard');
};

export const createFormThroughStep4 = (keepOffline: boolean): void => {
  getByTestId('dashboard-filters-new-report').not('.disabled').click();

  cy.url().should('include', '/new-report');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    getByTestId('confirm-dialog-no-button').should('not.be', 'visible');
    // cy.wait(1000);
  }

  type('ripa-calendar-form-date-picker', dayjs().format('MM/DD/YYYY'));

  getByTestId('ripa-form-container-continue').click();

  type('ripa-time-form-input', '00:00');

  if (keepOffline) {
    // Wait for the dialog and continue offline
    getByTestId('confirm-dialog-no-button', { timeout: 6000 }).click();
    getByTestId('confirm-dialog-no-button').should('not.be', 'visible');
    // cy.wait(1000);
  }

  getByTestId('ripa-form-container-continue').click();

  // Wait for the response to call form to be visible
  getByTestId('ripa-response-to-call-form').should('be.visible');
  getByTestId('ripa-response-to-call-yes').should('be.visible').click();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-duration-form-20').should('be.visible').click();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-location-form-school-switch').should('be.visible').click();
  getByTestId('ripa-location-form-school-autocomplete').should('be.visible').click();

  cy.get('*[data-option-index=2]').click();

  clearSnackBars();

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-people-form-people-counter-plus').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-label-form-label-input-0', 'label 0');

  type('ripa-label-form-label-input-1', 'label 1');

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-gender-form-box-0-0').click();

  getByTestId('ripa-gender-form-box-1-3').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-age-form-input-0', '12');

  getByTestId('ripa-age-form-student-switch-0').click();

  type('ripa-age-form-input-1', '22');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-race-form-0-0',
    'ripa-race-form-1-1',
    'ripa-form-container-continue',
    'ripa-disability-form-english-switch-1',
    'ripa-disability-form-disability-switch-person-1',
    'ripa-form-container-continue',
    'ripa-disability-details-form-box-1-4',
    'ripa-form-container-continue',
    'ripa-primary-reason-form-box-k12-0',
    'ripa-form-container-continue',
    'ripa-code-section-form-box-0',
    'ripa-code-section-form-autocomplete-input',
  ]);

  cy.get('*[data-option-index=2]').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-description-form-input', '123456');

  getByTestId('ripa-form-container-continue').click();

  getByTestId('ripa-primary-reason-form-box-3').click();

  getByTestId('ripa-form-container-continue').click();

  type('ripa-description-form-input', '4563456');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-action-tab-4',
    'ripa-action-taken-form-box-4-0',
    'ripa-action-taken-form-consent-switch-4-0',
    'ripa-action-taken-form-box-4-1',
    'ripa-action-taken-form-box-4-4',
    'ripa-form-container-continue',
    'ripa-search-basis-form-box-5',
    'ripa-form-container-continue',
  ]);

  type('ripa-search-description-form-input', '546456');

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-seizure-basis-form-box-1',
    'ripa-form-container-continue',
    'ripa-seizure-type-form-box-1',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-1',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-0',
    'ripa-result-of-stop-form-autocomplete-input-0-0',
  ]);

  cy.get('*[data-option-index=10]').click();

  clickTestIds([
    'ripa-form-container-continue',
    'ripa-action-taken-form-box-0-3',
    'ripa-form-container-continue',
    'ripa-contraband-form-box-5',
    'ripa-form-container-continue',
    'ripa-result-of-stop-form-box-0-1',
    'ripa-result-of-stop-form-autocomplete-input-0-1',
  ]);

  cy.get('*[data-option-index=10]').click();

  getByTestId('ripa-form-container-continue').click();
};
