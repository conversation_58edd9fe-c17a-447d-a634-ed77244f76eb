export const getByTestId = (
  dataTestId: string,
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow & Cypress.Withinable>
): Cypress.Chainable<JQuery<HTMLElement>> => cy.get(`*[data-testid=${dataTestId}]`, options);

interface TypeOptions {
  validate?: boolean;
}
export const typeLogin = (testId: string, text: string, { validate = true }:
   TypeOptions = {}): Cypress.Chainable<JQuery<HTMLElement>> => {
  getByTestId(testId).type(text);
  if (validate) {
    return getByTestId(testId).should('have.value', text);
  }
  return getByTestId(testId);
};

export const type = (testId: string, text: string, { validate = true }:
  TypeOptions = {}): Cypress.Chainable<JQuery<HTMLElement>> => {
    getByTestId(testId).type(text);
    if (validate) {
      return getByTestId(testId).should('have.value', text);
    }
    return getByTestId(testId);
  };

interface ClickOptions {
  force?: boolean;
  multiple?: boolean;
}

export const clickTestIds = (testIds: string[], { force = false, multiple = false }: ClickOptions = {}): void =>
  testIds.forEach((testId: string) => getByTestId(testId).should('be.visible').click({ force, multiple }));

export const clearSnackBars = (): void => {
  cy.get('.snackbar__box:not(.fading):not(.dead) .snackbar__box-close').click({
    multiple: true,
  });
  cy.get('.snackbar__box', { timeout: 10000 }).should('not.exist');
};
